[{"C:\\laragon\\www\\react-news\\frontend\\src\\index.js": "1", "C:\\laragon\\www\\react-news\\frontend\\src\\App.js": "2", "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js": "3", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js": "4", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js": "5", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js": "6", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js": "7", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js": "8", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js": "9", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js": "10", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js": "11", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js": "12", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js": "13", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js": "14", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js": "15", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js": "16", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js": "17", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js": "18", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js": "19", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\data-news.js": "20", "C:\\laragon\\www\\REACT\\frontend\\src\\index.js": "21", "C:\\laragon\\www\\REACT\\frontend\\src\\App.js": "22", "C:\\laragon\\www\\REACT\\frontend\\src\\reportWebVitals.js": "23", "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\user\\data-news.js": "24", "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\user\\LandingPage.js": "25", "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\user\\components\\Saved.js": "26", "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\admin\\auth\\Login.js": "27", "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\admin\\auth\\Register.js": "28", "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\user\\components\\Footer.js": "29", "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\admin\\swal.js": "30", "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\admin\\auth\\authService.js": "31"}, {"size": 535, "mtime": 1752195971363, "results": "32", "hashOfConfig": "33"}, {"size": 2642, "mtime": 1752460017363, "results": "34", "hashOfConfig": "33"}, {"size": 362, "mtime": 1752195971670, "results": "35", "hashOfConfig": "33"}, {"size": 68845, "mtime": 1752469319725, "results": "36", "hashOfConfig": "33"}, {"size": 243, "mtime": 1752201497899, "results": "37", "hashOfConfig": "33"}, {"size": 662, "mtime": 1752201773682, "results": "38", "hashOfConfig": "33"}, {"size": 313, "mtime": 1752201773682, "results": "39", "hashOfConfig": "33"}, {"size": 822, "mtime": 1752201773678, "results": "40", "hashOfConfig": "33"}, {"size": 688, "mtime": 1752201773679, "results": "41", "hashOfConfig": "33"}, {"size": 749, "mtime": 1752201773681, "results": "42", "hashOfConfig": "33"}, {"size": 5407, "mtime": 1752367525610, "results": "43", "hashOfConfig": "33"}, {"size": 218, "mtime": 1752206025063, "results": "44", "hashOfConfig": "33"}, {"size": 4157, "mtime": 1752220101050, "results": "45", "hashOfConfig": "33"}, {"size": 16189, "mtime": 1752462394344, "results": "46", "hashOfConfig": "33"}, {"size": 0, "mtime": 1752306675896, "results": "47", "hashOfConfig": "33"}, {"size": 11682, "mtime": 1752310168602, "results": "48", "hashOfConfig": "33"}, {"size": 13390, "mtime": 1752310218662, "results": "49", "hashOfConfig": "33"}, {"size": 16930, "mtime": 1752310279691, "results": "50", "hashOfConfig": "33"}, {"size": 4742, "mtime": 1752366365655, "results": "51", "hashOfConfig": "33"}, {"size": 21323, "mtime": 1752468163423, "results": "52", "hashOfConfig": "33"}, {"size": 535, "mtime": 1752195971363, "results": "53", "hashOfConfig": "54"}, {"size": 2642, "mtime": 1752460017363, "results": "55", "hashOfConfig": "54"}, {"size": 362, "mtime": 1752195971670, "results": "56", "hashOfConfig": "54"}, {"size": 21323, "mtime": 1752468163423, "results": "57", "hashOfConfig": "54"}, {"size": 69639, "mtime": 1752473150999, "results": "58", "hashOfConfig": "54"}, {"size": 16189, "mtime": 1752462394344, "results": "59", "hashOfConfig": "54"}, {"size": 3534, "mtime": 1752368607654, "results": "60", "hashOfConfig": "54"}, {"size": 5407, "mtime": 1752367525610, "results": "61", "hashOfConfig": "54"}, {"size": 313, "mtime": 1752201773682, "results": "62", "hashOfConfig": "54"}, {"size": 218, "mtime": 1752206025063, "results": "63", "hashOfConfig": "54"}, {"size": 4742, "mtime": 1752366365655, "results": "64", "hashOfConfig": "54"}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1umod7j", {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mqprx0", {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\laragon\\www\\react-news\\frontend\\src\\index.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\App.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js", ["158"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\data-news.js", ["159", "160", "161", "162"], [], "C:\\laragon\\www\\REACT\\frontend\\src\\index.js", [], [], "C:\\laragon\\www\\REACT\\frontend\\src\\App.js", [], [], "C:\\laragon\\www\\REACT\\frontend\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\user\\data-news.js", ["163", "164", "165", "166"], [], "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\user\\LandingPage.js", ["167"], [], "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\user\\components\\Saved.js", [], [], "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\admin\\auth\\Login.js", ["168"], [], "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\admin\\auth\\Register.js", [], [], "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\user\\components\\Footer.js", [], [], "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\admin\\swal.js", [], [], "C:\\laragon\\www\\REACT\\frontend\\src\\pages\\admin\\auth\\authService.js", [], [], {"ruleId": "169", "severity": 1, "message": "170", "line": 17, "column": 8, "nodeType": "171", "messageId": "172", "endLine": 17, "endColumn": 14}, {"ruleId": "169", "severity": 1, "message": "173", "line": 18, "column": 11, "nodeType": "171", "messageId": "172", "endLine": 18, "endColumn": 20}, {"ruleId": "169", "severity": 1, "message": "174", "line": 24, "column": 12, "nodeType": "171", "messageId": "172", "endLine": 24, "endColumn": 21}, {"ruleId": "169", "severity": 1, "message": "175", "line": 24, "column": 23, "nodeType": "171", "messageId": "172", "endLine": 24, "endColumn": 35}, {"ruleId": "176", "severity": 1, "message": "177", "line": 32, "column": 8, "nodeType": "178", "endLine": 32, "endColumn": 12, "suggestions": "179"}, {"ruleId": "169", "severity": 1, "message": "173", "line": 18, "column": 11, "nodeType": "171", "messageId": "172", "endLine": 18, "endColumn": 20}, {"ruleId": "169", "severity": 1, "message": "174", "line": 24, "column": 12, "nodeType": "171", "messageId": "172", "endLine": 24, "endColumn": 21}, {"ruleId": "169", "severity": 1, "message": "175", "line": 24, "column": 23, "nodeType": "171", "messageId": "172", "endLine": 24, "endColumn": 35}, {"ruleId": "176", "severity": 1, "message": "177", "line": 32, "column": 8, "nodeType": "178", "endLine": 32, "endColumn": 12, "suggestions": "180"}, {"ruleId": "169", "severity": 1, "message": "170", "line": 17, "column": 8, "nodeType": "171", "messageId": "172", "endLine": 17, "endColumn": 14}, {"ruleId": "169", "severity": 1, "message": "181", "line": 2, "column": 53, "nodeType": "171", "messageId": "172", "endLine": 2, "endColumn": 69}, "no-unused-vars", "'Footer' is defined but never used.", "Identifier", "unusedVar", "'isDesktop' is assigned a value but never used.", "'bottomNav' is assigned a value but never used.", "'setBottomNav' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchNewsDetail'. Either include it or remove the dependency array.", "ArrayExpression", ["182"], ["183"], "'CircularProgress' is defined but never used.", {"desc": "184", "fix": "185"}, {"desc": "184", "fix": "186"}, "Update the dependencies array to be: [fetchNewsDetail, id]", {"range": "187", "text": "188"}, {"range": "189", "text": "188"}, [1246, 1250], "[fetchNewsDetail, id]", [1246, 1250]]